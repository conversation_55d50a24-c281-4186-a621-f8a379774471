"""
Repository layer for the User Management Service.

This module provides data access methods for managing users, sessions,
activities, and preferences in the database.
"""

from datetime import datetime, timedelta, timezone
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_, func
from sqlalchemy.exc import SQLAlchemyError
import logging

from models import User, UserSession, UserActivity, UserPreference, PasswordHistory, UserRole, UserStatus
from schemas import UserCreate, UserUpdate

logger = logging.getLogger(__name__)


class UserRepository:
    """Repository for managing user data."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_user(self, user_data: UserCreate, password_hash: str) -> User:
        """Create a new user."""
        try:
            user = User(
                email=user_data.email,
                password_hash=password_hash,
                first_name=user_data.first_name,
                last_name=user_data.last_name,
                grade_level=user_data.grade_level,
                school_id=user_data.school_id,
                parent_email=user_data.parent_email,
                role=UserRole.STUDENT,  # Default role
                status=UserStatus.PENDING_VERIFICATION,
                verification_token=self._generate_verification_token()
            )
            
            self.db.add(user)
            await self.db.commit()
            await self.db.refresh(user)
            
            logger.info(f"Created user with email {user_data.email}")
            return user
            
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Error creating user: {e}")
            raise
    
    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Get user by ID."""
        try:
            stmt = select(User).where(
                and_(
                    User.id == user_id,
                    User.deleted_at.is_(None)
                )
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
            
        except SQLAlchemyError as e:
            logger.error(f"Error retrieving user by ID: {e}")
            raise
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        try:
            stmt = select(User).where(
                and_(
                    User.email == email,
                    User.deleted_at.is_(None)
                )
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
            
        except SQLAlchemyError as e:
            logger.error(f"Error retrieving user by email: {e}")
            raise
    
    async def update_user(self, user_id: int, user_data: UserUpdate) -> Optional[User]:
        """Update user information."""
        try:
            # Build update dictionary with only non-None values
            update_data = {}
            if user_data.first_name is not None:
                update_data['first_name'] = user_data.first_name
            if user_data.last_name is not None:
                update_data['last_name'] = user_data.last_name
            if user_data.phone_number is not None:
                update_data['phone_number'] = user_data.phone_number
            if user_data.date_of_birth is not None:
                update_data['date_of_birth'] = user_data.date_of_birth
            if user_data.grade_level is not None:
                update_data['grade_level'] = user_data.grade_level
            if user_data.school_id is not None:
                update_data['school_id'] = user_data.school_id
            if user_data.parent_email is not None:
                update_data['parent_email'] = user_data.parent_email
            if user_data.avatar_url is not None:
                update_data['avatar_url'] = user_data.avatar_url
            if user_data.timezone is not None:
                update_data['timezone'] = user_data.timezone
            if user_data.language is not None:
                update_data['language'] = user_data.language
            if user_data.email_notifications is not None:
                update_data['email_notifications'] = user_data.email_notifications
            if user_data.push_notifications is not None:
                update_data['push_notifications'] = user_data.push_notifications
            
            if not update_data:
                # No updates to make
                return await self.get_user_by_id(user_id)
            
            # Add updated_at timestamp
            update_data['updated_at'] = datetime.now(timezone.utc)
            
            stmt = update(User).where(
                and_(
                    User.id == user_id,
                    User.deleted_at.is_(None)
                )
            ).values(**update_data)
            
            await self.db.execute(stmt)
            await self.db.commit()
            
            # Return updated user
            return await self.get_user_by_id(user_id)
            
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Error updating user: {e}")
            raise
    
    async def verify_user_email(self, verification_token: str) -> Optional[User]:
        """Verify user email with token."""
        try:
            stmt = update(User).where(
                and_(
                    User.verification_token == verification_token,
                    User.deleted_at.is_(None)
                )
            ).values(
                is_verified=True,
                status=UserStatus.ACTIVE,
                verification_token=None,
                updated_at=datetime.now(timezone.utc)
            )
            
            result = await self.db.execute(stmt)
            await self.db.commit()
            
            if result.rowcount > 0:
                # Get the updated user
                user_stmt = select(User).where(User.is_verified == True)
                user_result = await self.db.execute(user_stmt)
                return user_result.scalar_one_or_none()
            
            return None
            
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Error verifying user email: {e}")
            raise
    
    async def update_password(self, user_id: int, new_password_hash: str) -> bool:
        """Update user password."""
        try:
            # Store old password in history
            current_user = await self.get_user_by_id(user_id)
            if current_user:
                password_history = PasswordHistory(
                    user_id=user_id,
                    password_hash=current_user.password_hash
                )
                self.db.add(password_history)
            
            # Update password
            stmt = update(User).where(
                and_(
                    User.id == user_id,
                    User.deleted_at.is_(None)
                )
            ).values(
                password_hash=new_password_hash,
                updated_at=datetime.now(timezone.utc)
            )
            
            result = await self.db.execute(stmt)
            await self.db.commit()
            
            return result.rowcount > 0

        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Error updating password: {e}")
            raise

    async def update_last_login(self, user_id: int) -> bool:
        """Update user's last login timestamp."""
        try:
            stmt = update(User).where(
                and_(
                    User.id == user_id,
                    User.deleted_at.is_(None)
                )
            ).values(
                last_login=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )

            result = await self.db.execute(stmt)
            await self.db.commit()

            return result.rowcount > 0

        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Error updating last login: {e}")
            raise
    
    async def create_session(self, user_id: int, session_token: str, refresh_token: str, 
                           expires_at: datetime, device_info: str = None, 
                           ip_address: str = None, user_agent: str = None) -> UserSession:
        """Create a new user session."""
        try:
            session = UserSession(
                user_id=user_id,
                session_token=session_token,
                refresh_token=refresh_token,
                expires_at=expires_at,
                device_info=device_info,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            self.db.add(session)
            await self.db.commit()
            await self.db.refresh(session)
            
            return session
            
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Error creating session: {e}")
            raise
    
    async def get_session_by_token(self, session_token: str) -> Optional[UserSession]:
        """Get session by token."""
        try:
            stmt = select(UserSession).where(
                and_(
                    UserSession.session_token == session_token,
                    UserSession.is_active == True,
                    UserSession.expires_at > datetime.now(timezone.utc)
                )
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
            
        except SQLAlchemyError as e:
            logger.error(f"Error retrieving session: {e}")
            raise
    
    async def revoke_session(self, session_token: str) -> bool:
        """Revoke a user session."""
        try:
            stmt = update(UserSession).where(
                UserSession.session_token == session_token
            ).values(
                is_active=False,
                revoked_at=datetime.now(timezone.utc)
            )
            
            result = await self.db.execute(stmt)
            await self.db.commit()
            
            return result.rowcount > 0
            
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Error revoking session: {e}")
            raise
    
    async def log_activity(self, user_id: Optional[int], activity_type: str, 
                          activity_description: str = None, resource_type: str = None,
                          resource_id: str = None, ip_address: str = None,
                          user_agent: str = None, request_method: str = None,
                          request_path: str = None, status_code: int = None,
                          error_message: str = None) -> UserActivity:
        """Log user activity."""
        try:
            activity = UserActivity(
                user_id=user_id,
                activity_type=activity_type,
                activity_description=activity_description,
                resource_type=resource_type,
                resource_id=resource_id,
                ip_address=ip_address,
                user_agent=user_agent,
                request_method=request_method,
                request_path=request_path,
                status_code=status_code,
                error_message=error_message
            )
            
            self.db.add(activity)
            await self.db.commit()
            await self.db.refresh(activity)
            
            return activity
            
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Error logging activity: {e}")
            raise
    
    def _generate_verification_token(self) -> str:
        """Generate a verification token."""
        import secrets
        return secrets.token_urlsafe(32)
