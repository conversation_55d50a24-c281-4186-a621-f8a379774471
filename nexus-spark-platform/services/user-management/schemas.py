"""
Pydantic schemas for the User Management microservice.

This module defines the data models used for user authentication,
authorization, and profile management.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, EmailStr
from enum import Enum


class UserRole(str, Enum):
    """User roles in the system."""
    STUDENT = "student"
    TEACHER = "teacher"
    ADMIN = "admin"
    PARENT = "parent"
    CONTENT_CREATOR = "content_creator"
    ANALYST = "analyst"


class UserStatus(str, Enum):
    """User account status."""
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    SUSPENDED = "SUSPENDED"
    PENDING_VERIFICATION = "PENDING_VERIFICATION"


class GradeLevel(int, Enum):
    """Grade levels supported by the platform."""
    GRADE_1 = 1
    GRADE_2 = 2
    GRADE_3 = 3
    GRADE_4 = 4
    GRADE_5 = 5
    GRADE_6 = 6
    GRADE_7 = 7
    GRADE_8 = 8
    GRADE_9 = 9
    GRADE_10 = 10
    GRADE_11 = 11
    GRADE_12 = 12


class UserBase(BaseModel):
    """Base user schema with common fields."""
    email: EmailStr = Field(..., description="User's email address")
    first_name: str = Field(..., min_length=1, max_length=50, description="User's first name")
    last_name: str = Field(..., min_length=1, max_length=50, description="User's last name")
    role: UserRole = Field(default=UserRole.STUDENT, description="User's role in the system")
    is_active: bool = Field(default=True, description="Whether the user account is active")


class UserCreate(UserBase):
    """Schema for creating new users."""
    password: str = Field(..., min_length=8, max_length=128, description="User's password")
    confirm_password: str = Field(..., description="Password confirmation")
    
    # Optional profile fields
    grade_level: Optional[GradeLevel] = Field(None, description="Student's grade level")
    school_id: Optional[str] = Field(None, description="School identifier")
    parent_email: Optional[EmailStr] = Field(None, description="Parent's email (for students)")
    
    def validate_passwords_match(self):
        """Validate that password and confirm_password match."""
        if self.password != self.confirm_password:
            raise ValueError("Passwords do not match")
        return self


class UserUpdate(BaseModel):
    """Schema for updating user information."""
    first_name: Optional[str] = Field(None, min_length=1, max_length=50)
    last_name: Optional[str] = Field(None, min_length=1, max_length=50)
    email: Optional[EmailStr] = Field(None)
    grade_level: Optional[GradeLevel] = Field(None)
    school_id: Optional[str] = Field(None)
    preferences: Optional[Dict[str, Any]] = Field(None)
    is_active: Optional[bool] = Field(None)


class UserProfile(BaseModel):
    """Extended user profile schema."""
    id: int = Field(..., description="Unique user identifier")
    email: EmailStr = Field(..., description="User's email address")
    first_name: str = Field(..., description="User's first name")
    last_name: str = Field(..., description="User's last name")
    role: UserRole = Field(..., description="User's role")
    status: UserStatus = Field(..., description="Account status")
    
    # Profile details
    grade_level: Optional[GradeLevel] = Field(None, description="Student's grade level")
    school_id: Optional[str] = Field(None, description="School identifier")
    school_name: Optional[str] = Field(None, description="School name")
    
    # Preferences and settings
    preferences: Dict[str, Any] = Field(default_factory=dict, description="User preferences")
    notification_settings: Dict[str, bool] = Field(default_factory=dict, description="Notification preferences")
    
    # Activity tracking
    last_login: Optional[datetime] = Field(None, description="Last login timestamp")
    login_count: int = Field(default=0, description="Total number of logins")
    created_at: datetime = Field(..., description="Account creation timestamp")
    updated_at: datetime = Field(..., description="Last profile update timestamp")
    
    # Learning data
    topics_completed: int = Field(default=0, description="Number of topics completed")
    assessments_taken: int = Field(default=0, description="Number of assessments taken")
    average_score: Optional[float] = Field(None, description="Average assessment score")
    
    class Config:
        from_attributes = True


class User(UserProfile):
    """Complete user schema including sensitive fields."""
    password_hash: str = Field(..., description="Hashed password")
    email_verified: bool = Field(default=False, description="Whether email is verified")
    verification_token: Optional[str] = Field(None, description="Email verification token")
    reset_token: Optional[str] = Field(None, description="Password reset token")
    reset_token_expires: Optional[datetime] = Field(None, description="Reset token expiration")


class UserLogin(BaseModel):
    """Schema for user login requests."""
    email: EmailStr = Field(..., description="User's email address")
    password: str = Field(..., description="User's password")
    remember_me: bool = Field(default=False, description="Whether to remember the login")


class UserLoginResponse(BaseModel):
    """Schema for login response."""
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")
    user: UserProfile = Field(..., description="User profile information")


class TokenRefresh(BaseModel):
    """Schema for token refresh requests."""
    refresh_token: str = Field(..., description="Refresh token")


class PasswordReset(BaseModel):
    """Schema for password reset requests."""
    email: EmailStr = Field(..., description="User's email address")


class PasswordResetConfirm(BaseModel):
    """Schema for password reset confirmation."""
    token: str = Field(..., description="Password reset token")
    new_password: str = Field(..., min_length=8, max_length=128, description="New password")
    confirm_password: str = Field(..., description="Password confirmation")
    
    def validate_passwords_match(self):
        """Validate that password and confirm_password match."""
        if self.new_password != self.confirm_password:
            raise ValueError("Passwords do not match")
        return self


class PasswordChange(BaseModel):
    """Schema for password change requests."""
    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, max_length=128, description="New password")
    confirm_password: str = Field(..., description="Password confirmation")
    
    def validate_passwords_match(self):
        """Validate that password and confirm_password match."""
        if self.new_password != self.confirm_password:
            raise ValueError("Passwords do not match")
        return self


class EmailVerification(BaseModel):
    """Schema for email verification."""
    token: str = Field(..., description="Email verification token")


class UserSearch(BaseModel):
    """Schema for user search queries."""
    query: Optional[str] = Field(None, description="Search query (name, email)")
    role: Optional[UserRole] = Field(None, description="Filter by role")
    status: Optional[UserStatus] = Field(None, description="Filter by status")
    grade_level: Optional[GradeLevel] = Field(None, description="Filter by grade level")
    school_id: Optional[str] = Field(None, description="Filter by school")
    limit: int = Field(default=50, le=200, description="Maximum number of results")
    offset: int = Field(default=0, ge=0, description="Number of results to skip")


class UserSearchResponse(BaseModel):
    """Schema for user search results."""
    users: List[UserProfile] = Field(..., description="List of matching users")
    total_count: int = Field(..., description="Total number of matching users")
    has_more: bool = Field(..., description="Whether there are more results")


class UserActivity(BaseModel):
    """Schema for user activity tracking."""
    user_id: str = Field(..., description="User identifier")
    activity_type: str = Field(..., description="Type of activity")
    activity_data: Dict[str, Any] = Field(default_factory=dict, description="Activity details")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Activity timestamp")
    ip_address: Optional[str] = Field(None, description="User's IP address")
    user_agent: Optional[str] = Field(None, description="User's browser/device info")


class UserSession(BaseModel):
    """Schema for user session information."""
    session_id: str = Field(..., description="Session identifier")
    user_id: str = Field(..., description="User identifier")
    created_at: datetime = Field(..., description="Session creation time")
    last_activity: datetime = Field(..., description="Last activity time")
    ip_address: Optional[str] = Field(None, description="Session IP address")
    user_agent: Optional[str] = Field(None, description="Session user agent")
    is_active: bool = Field(default=True, description="Whether session is active")


class UserStats(BaseModel):
    """Schema for user statistics."""
    total_users: int = Field(..., description="Total number of users")
    active_users: int = Field(..., description="Number of active users")
    new_users_today: int = Field(..., description="New users registered today")
    new_users_week: int = Field(..., description="New users registered this week")
    users_by_role: Dict[str, int] = Field(..., description="User count by role")
    users_by_grade: Dict[str, int] = Field(..., description="User count by grade level")
    login_activity: Dict[str, int] = Field(..., description="Login activity statistics")


class BulkUserCreate(BaseModel):
    """Schema for bulk user creation."""
    users: List[UserCreate] = Field(..., min_items=1, max_items=100, description="List of users to create")
    send_welcome_email: bool = Field(default=True, description="Whether to send welcome emails")
    auto_verify_email: bool = Field(default=False, description="Whether to auto-verify emails")


class BulkUserResponse(BaseModel):
    """Schema for bulk user creation response."""
    created_users: List[UserProfile] = Field(..., description="Successfully created users")
    failed_users: List[Dict[str, Any]] = Field(..., description="Failed user creations with errors")
    total_created: int = Field(..., description="Total number of users created")
    total_failed: int = Field(..., description="Total number of failed creations")


class UserPermission(BaseModel):
    """Schema for user permissions."""
    permission_id: str = Field(..., description="Permission identifier")
    name: str = Field(..., description="Permission name")
    description: str = Field(..., description="Permission description")
    resource: str = Field(..., description="Resource this permission applies to")
    action: str = Field(..., description="Action this permission allows")


class UserRoleSchema(BaseModel):
    """Schema for user roles with permissions."""
    role_id: str = Field(..., description="Role identifier")
    name: str = Field(..., description="Role name")
    description: str = Field(..., description="Role description")
    permissions: List[UserPermission] = Field(..., description="Permissions granted by this role")
    is_system_role: bool = Field(default=False, description="Whether this is a system-defined role")
