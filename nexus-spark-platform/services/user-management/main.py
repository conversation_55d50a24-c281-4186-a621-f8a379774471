"""
FastAPI application for the User Management microservice.

This module implements user authentication, authorization, and profile
management for the Nexus-Spark platform.
"""

import os
import logging
from datetime import datetime, timedelta
from typing import List, Optional
from fastapi import FastAPI, HTTPException, Depends, status, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import H<PERSON><PERSON><PERSON>earer, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncSession
from sqlalchemy.exc import SQLAlchemyError
from jose import jwt
from passlib.context import CryptContext

from schemas import (
    UserCreate, UserUpdate, UserProfile, User, UserLogin, UserLoginResponse,
    TokenRefresh, PasswordReset, PasswordResetConfirm, PasswordChange,
    EmailVerification, UserSearch, UserSearchResponse, UserActivity,
    UserSession, UserStats, BulkUserCreate, BulkUserResponse,
    UserRole
)
from auth import AuthManager
from models import Base, UserStatus
from repository import UserRepository

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
DATABASE_URL = os.getenv(
    "DATABASE_URL", 
    "postgresql+asyncpg://postgres:password@localhost:5432/nexus_spark_db"
)

# Create async engine and session factory
engine = create_async_engine(
    DATABASE_URL,
    echo=False,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    pool_recycle=3600,
)
async_session_maker = async_sessionmaker(
    engine,
    expire_on_commit=False,
    autoflush=False
)

# FastAPI application instance
app = FastAPI(
    title="User Management Service",
    description="User authentication, authorization, and profile management service",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize components
auth_manager = AuthManager()
security = HTTPBearer()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


async def get_db() -> AsyncSession:
    """Dependency function to provide database session."""
    async with async_session_maker() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            await session.close()


def get_user_repository(db: AsyncSession = Depends(get_db)) -> UserRepository:
    """Dependency function to provide UserRepository instance."""
    return UserRepository(db)


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    repository: UserRepository = Depends(get_user_repository)
) -> UserProfile:
    """Get current authenticated user."""
    try:
        token = credentials.credentials
        payload = auth_manager.verify_token(token)
        user_id = payload.get("sub")
        
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials"
            )
        
        user = await repository.get_user_by_id(user_id)
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found"
            )
        
        return UserProfile.model_validate(user)
        
    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )


def require_role(required_role: UserRole):
    """Dependency to require specific user role."""
    def role_checker(current_user: UserProfile = Depends(get_current_user)):
        if current_user.role != required_role and current_user.role != UserRole.ADMIN:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        return current_user
    return role_checker


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "user-management-service",
        "timestamp": datetime.utcnow().isoformat()
    }


@app.post("/v1/auth/register", response_model=UserProfile)
async def register_user(
    user_data: UserCreate,
    background_tasks: BackgroundTasks,
    repository: UserRepository = Depends(get_user_repository)
):
    """
    Register a new user.
    
    This endpoint creates a new user account with the provided information
    and sends a verification email.
    """
    try:
        # Validate password confirmation
        user_data.validate_passwords_match()
        
        # Check if user already exists
        existing_user = await repository.get_user_by_email(user_data.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email already exists"
            )
        
        # Hash password
        password_hash = pwd_context.hash(user_data.password)
        
        # Create user
        user = await repository.create_user(user_data, password_hash)
        
        # Send verification email in background
        background_tasks.add_task(
            send_verification_email, 
            user.email, 
            user.verification_token
        )
        
        return UserProfile.model_validate(user)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except SQLAlchemyError as e:
        logger.error(f"Database error in register_user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user account"
        )


@app.post("/v1/auth/login", response_model=UserLoginResponse)
async def login_user(
    login_data: UserLogin,
    repository: UserRepository = Depends(get_user_repository)
):
    """
    Authenticate user and return access tokens.
    
    This endpoint validates user credentials and returns JWT tokens
    for accessing protected resources.
    """
    try:
        # Get user by email
        user = await repository.get_user_by_email(login_data.email)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )
        
        # Verify password
        if not pwd_context.verify(login_data.password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )
        
        # Check if user is active
        if user.status != UserStatus.ACTIVE:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Account is inactive or pending verification"
            )
        
        # Generate tokens
        access_token = auth_manager.create_access_token(
            data={"sub": str(user.id), "email": user.email, "role": user.role.value}
        )
        refresh_token = auth_manager.create_refresh_token(
            data={"sub": str(user.id)}
        )

        # Update last login
        await repository.update_last_login(user.id)
        
        return UserLoginResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=auth_manager.access_token_expire_minutes * 60,
            user=UserProfile.model_validate(user)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in login_user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@app.post("/v1/auth/refresh", response_model=UserLoginResponse)
async def refresh_token(
    token_data: TokenRefresh,
    repository: UserRepository = Depends(get_user_repository)
):
    """
    Refresh access token using refresh token.
    
    This endpoint validates a refresh token and returns new access
    and refresh tokens.
    """
    try:
        # Verify refresh token
        payload = auth_manager.verify_refresh_token(token_data.refresh_token)
        user_id = payload.get("sub")
        
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
        
        # Get user
        user = await repository.get_user_by_id(user_id)
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found or inactive"
            )
        
        # Generate new tokens
        access_token = auth_manager.create_access_token(
            data={"sub": user.user_id, "email": user.email, "role": user.role}
        )
        new_refresh_token = auth_manager.create_refresh_token(
            data={"sub": user.user_id}
        )
        
        return UserLoginResponse(
            access_token=access_token,
            refresh_token=new_refresh_token,
            expires_in=auth_manager.access_token_expire_minutes * 60,
            user=UserProfile.model_validate(user)
        )
        
    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    except Exception as e:
        logger.error(f"Error in refresh_token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@app.get("/v1/users/me", response_model=UserProfile)
async def get_current_user_profile(
    current_user: UserProfile = Depends(get_current_user)
):
    """
    Get current user's profile.
    
    This endpoint returns the profile information for the
    currently authenticated user.
    """
    return current_user


@app.put("/v1/users/me", response_model=UserProfile)
async def update_current_user_profile(
    user_update: UserUpdate,
    current_user: UserProfile = Depends(get_current_user),
    repository: UserRepository = Depends(get_user_repository)
):
    """
    Update current user's profile.
    
    This endpoint allows users to update their own profile information.
    """
    try:
        updated_user = await repository.update_user(current_user.user_id, user_update)
        return UserProfile.model_validate(updated_user)
        
    except SQLAlchemyError as e:
        logger.error(f"Database error in update_current_user_profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update profile"
        )


@app.post("/v1/users/change-password")
async def change_password(
    password_data: PasswordChange,
    current_user: UserProfile = Depends(get_current_user),
    repository: UserRepository = Depends(get_user_repository)
):
    """
    Change user's password.
    
    This endpoint allows users to change their password by providing
    their current password and a new password.
    """
    try:
        # Validate password confirmation
        password_data.validate_passwords_match()
        
        # Get user with password hash
        user = await repository.get_user_by_id(current_user.user_id)
        
        # Verify current password
        if not pwd_context.verify(password_data.current_password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Current password is incorrect"
            )
        
        # Hash new password
        new_password_hash = pwd_context.hash(password_data.new_password)
        
        # Update password
        await repository.update_password(current_user.user_id, new_password_hash)
        
        return {"message": "Password changed successfully"}
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in change_password: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to change password"
        )


@app.get("/v1/users", response_model=UserSearchResponse)
async def search_users(
    query: Optional[str] = None,
    role: Optional[UserRole] = None,
    limit: int = 50,
    offset: int = 0,
    current_user: UserProfile = Depends(require_role(UserRole.ADMIN)),
    repository: UserRepository = Depends(get_user_repository)
):
    """
    Search users (admin only).
    
    This endpoint allows administrators to search and filter users
    based on various criteria.
    """
    try:
        search_params = UserSearch(
            query=query,
            role=role,
            limit=limit,
            offset=offset
        )
        
        users, total_count = await repository.search_users(search_params)
        
        return UserSearchResponse(
            users=[UserProfile.model_validate(user) for user in users],
            total_count=total_count,
            has_more=(offset + len(users)) < total_count
        )
        
    except Exception as e:
        logger.error(f"Error in search_users: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to search users"
        )


async def send_verification_email(email: str, token: str):
    """Send email verification email (background task)."""
    try:
        # In production, this would send an actual email
        logger.info(f"Sending verification email to {email} with token {token}")
        # Implementation would use email service like SendGrid, SES, etc.
    except Exception as e:
        logger.error(f"Failed to send verification email: {e}")


@app.on_event("startup")
async def startup_event():
    """Initialize the service on startup."""
    try:
        # Create tables if they don't exist
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("User Management Service started successfully")
    except Exception as e:
        logger.error(f"Failed to start User Management Service: {e}")
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    await engine.dispose()
    logger.info("User Management Service shut down successfully")
